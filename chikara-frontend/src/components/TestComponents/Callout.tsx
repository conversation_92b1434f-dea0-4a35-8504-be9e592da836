import { cn } from "@/lib/utils";
import { Icon } from "./Icon";

interface CalloutProps {
    type?: "note" | "warning";
    title: string;
    children?: React.ReactNode;
    className?: string;
    titleSize?: string;
}

const styles = {
    note: {
        container: "bg-sky-50 dark:bg-slate-800/60 dark:ring-1 dark:ring-slate-300/10",
        title: "text-sky-900 dark:text-sky-400",
        body: "text-sky-800 [--tw-prose-background:var(--color-sky-50)] prose-a:text-sky-900 prose-code:text-sky-900 dark:text-slate-300 dark:prose-code:text-slate-300",
    },
    warning: {
        container: "alert alert-info alert-soft",
        title: "text-amber-900 dark:text-amber-500",
        body: "text-amber-800 [--tw-prose-underline:var(--color-amber-400)] [--tw-prose-background:var(--color-amber-50)] prose-a:text-amber-900 prose-code:text-amber-900 dark:text-slate-300 dark:[--tw-prose-underline:var(--color-sky-700)] dark:prose-code:text-slate-300",
    },
};

const icons = {
    // note: (props: React.SVGProps<SVGSVGElement>) => <Icon icon="lightbulb" {...props} />,
    warning: (props: React.SVGProps<SVGSVGElement>) => <Icon icon="warning" color="amber" {...props} />,
};

export function Callout({ type = "warning", title, children, className, titleSize }: CalloutProps) {
    const IconComponent = icons[type];

    return (
        <div className={cn(className, "mx-4 my-2 flex md:mx-0", styles[type].container)}>
            <IconComponent className="size-8 flex-none" />
            <div className="ml-4 flex-auto">
                <p className={cn(titleSize ? titleSize : "text-base", "m-0 font-display", styles[type].title)}>
                    {title}
                </p>
                {children && (
                    <div className={cn("prose mt-2 font-body text-sm md:text-base", styles[type].body)}>{children}</div>
                )}
            </div>
        </div>
    );
}
