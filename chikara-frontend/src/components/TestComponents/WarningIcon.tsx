import { cn } from "@/lib/utils";
import { useId } from "react";

const iconStyles = {
    blue: "[--icon-foreground:var(--color-slate-900)] [--icon-background:var(--color-white)]",
    amber: "[--icon-foreground:var(--color-amber-900)] [--icon-background:var(--color-amber-100)]",
};

const gradients = {
    blue: [{ stopColor: "#0EA5E9" }, { stopColor: "#22D3EE", offset: ".527" }, { stopColor: "#818CF8", offset: 1 }],
    amber: [
        { stopColor: "#FDE68A", offset: ".08" },
        { stopColor: "#F59E0B", offset: ".837" },
    ],
};

interface WarningIconProps {
    color?: "blue" | "amber";
    className?: string;
    [key: string]: unknown;
}

function Gradient({ color = "blue", ...props }: { color?: "blue" | "amber"; [key: string]: unknown }) {
    return (
        <radialGradient cx={0} cy={0} r={1} gradientUnits="userSpaceOnUse" {...props}>
            {gradients[color].map((stop: { stopColor: string; offset?: string | number }, stopIndex: number) => (
                <stop key={stopIndex} {...stop} />
            ))}
        </radialGradient>
    );
}

function LightMode({ className, ...props }: React.SVGProps<SVGSVGElement>) {
    return <g className={cn("dark:hidden", className)} {...props} />;
}

function DarkMode({ className, ...props }: React.SVGProps<SVGSVGElement>) {
    return <g className={cn("hidden dark:inline", className)} {...props} />;
}

export function WarningIcon({ color = "blue", className, ...props }: WarningIconProps) {
    const id = useId();

    return (
        <svg aria-hidden="true" viewBox="0 0 32 32" fill="none" className={cn(className, iconStyles[color])} {...props}>
            <defs>
                <Gradient
                    id={`${id}-gradient`}
                    color={color}
                    gradientTransform="rotate(65.924 1.519 20.92) scale(25.7391)"
                />
                <Gradient id={`${id}-gradient-dark`} color={color} gradientTransform="matrix(0 24.5 -24.5 0 16 5.5)" />
            </defs>
            <LightMode>
                <circle cx={20} cy={20} r={12} fill={`url(#${id}-gradient)`} />
                <path
                    d="M3 16c0 7.18 5.82 13 13 13s13-5.82 13-13S23.18 3 16 3 3 8.82 3 16Z"
                    fillOpacity={0.5}
                    className="fill-(--icon-background) stroke-(--icon-foreground)"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <path
                    d="m15.408 16.509-1.04-5.543a1.66 1.66 0 1 1 3.263 0l-1.039 5.543a.602.602 0 0 1-1.184 0Z"
                    className="fill-(--icon-foreground) stroke-(--icon-foreground)"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <path
                    d="M16 23a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
                    fillOpacity={0.5}
                    stroke="currentColor"
                    className="fill-(--icon-background) stroke-(--icon-foreground)"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </LightMode>
            <DarkMode>
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M2 16C2 8.268 8.268 2 16 2s14 6.268 14 14-6.268 14-14 14S2 23.732 2 16Zm11.386-4.85a2.66 2.66 0 1 1 5.228 0l-1.039 5.543a1.602 1.602 0 0 1-3.15 0l-1.04-5.543ZM16 20a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"
                    fill={`url(#${id}-gradient-dark)`}
                />
            </DarkMode>
        </svg>
    );
}
